const api = require('../utils/requestHelper');
const { describeWithTransaction } = require('../utils/describeWithTransaction');
const { User, UserProfile } = require('../../src/models');

describeWithTransaction('UsersController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin',
    });

    user = await User.create({
      name: 'Regular User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user',
    });
  });

  describe('GET /api/v1/users', () => {
    beforeEach(async () => {
      // Create additional test users
      await User.bulkCreate(
        [
          { name: '<PERSON>', email: '<EMAIL>', password: 'password', role: 'admin' },
          { name: '<PERSON>', email: '<EMAIL>', password: 'password', role: 'user' },
          { name: '<PERSON>', email: '<EMAIL>', password: 'password', role: 'user' },
          { name: '<PERSON>', email: '<EMAIL>', password: 'password', role: 'admin' },
        ],
        { individualHooks: true },
      );
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/users');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/users');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });

      it('should return 200 for admin users', async () => {
        const response = await api.as(admin).get('/api/v1/users');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(Array.isArray(response.body.data)).toBe(true);
      });
    });

    describe('basic functionality', () => {
      it('should return all users with pagination', async () => {
        const response = await api.as(admin).get('/api/v1/users');

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6); // 2 initial + 4 created in beforeEach
        expect(response.body).toHaveProperty('pagination');
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 10,
          total: 6,
          totalPages: 1,
        });
      });

      it('should handle pagination parameters', async () => {
        const params = { page: 1, limit: 3 };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 3,
          total: 6,
          totalPages: 2,
        });
      });
    });

    describe('filtering', () => {
      it('should filter users by role', async () => {
        const params = { role: 'admin' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3); // Admin User, John Doe, Alice Brown
        expect(response.body.data.every(user => user.role === 'admin')).toBe(true);
      });

      it('should filter users by name', async () => {
        const params = { name: 'John' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2); // John Doe, Bob Johnson
        expect(response.body.data.some(user => user.name.includes('John'))).toBe(true);
      });

      it('should filter users by email domain', async () => {
        const params = { email: '<EMAIL>' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1); // Jane Smith
        expect(response.body.data[0].email).toBe('<EMAIL>');
      });

      it('should search users by name and email', async () => {
        const params = { search: 'john' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThan(0);
        expect(
          response.body.data.every(
            user =>
              user.name.toLowerCase().includes('john') || user.email.toLowerCase().includes('john'),
          ),
        ).toBe(true);
      });

      it('should combine multiple filters', async () => {
        const params = { role: 'admin', name: 'John' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].name).toBe('John Doe');
        expect(response.body.data[0].role).toBe('admin');
      });
    });

    describe('sorting', () => {
      it('should sort users by name ascending', async () => {
        const params = { sort: 'name', sort_direction: 'asc' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6);

        const names = response.body.data.map(user => user.name);
        const sortedNames = [...names].sort();
        expect(names).toEqual(sortedNames);
      });

      it('should sort users by name descending', async () => {
        const params = { sort: 'name', sort_direction: 'desc' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6);

        const names = response.body.data.map(user => user.name);
        const sortedNames = [...names].sort().reverse();
        expect(names).toEqual(sortedNames);
      });

      it('should sort users by email', async () => {
        const params = { sort: 'email', sort_direction: 'asc' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6);

        const emails = response.body.data.map(user => user.email);
        const sortedEmails = [...emails].sort();
        expect(emails).toEqual(sortedEmails);
      });

      it('reject invalid sort columns', async () => {
        const params = { sort: 'invalid_column', sort_direction: 'asc' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('date filtering', () => {
      it('should filter users by created_after date', async () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const dateString = yesterday.toISOString().split('T')[0];

        const params = { created_after: dateString };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6); // All users created today
      });

      it('should filter users by created_before date', async () => {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dateString = tomorrow.toISOString().split('T')[0];

        const params = { created_before: dateString };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6); // All users created today
      });
    });

    describe('complex queries', () => {
      it('should handle complex query with multiple parameters', async () => {
        const params = {
          role: 'user',
          page: 1,
          limit: 2,
          sort: 'name',
          sort_direction: 'asc',
        };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.data.every(user => user.role === 'user')).toBe(true);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 2,
          total: 3, // Regular User, Jane Smith, Bob Johnson
          totalPages: 2,
        });
      });
    });

    describe('response format', () => {
      it('should return properly formatted user data', async () => {
        const params = { limit: 1 };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);

        const user = response.body.data[0];
        expect(user).toHaveProperty('id');
        expect(user).toHaveProperty('name');
        expect(user).toHaveProperty('email');
        expect(user).toHaveProperty('role');
        expect(user).not.toHaveProperty('password');
        expect(user).not.toHaveProperty('password_digest');
      });
    });
  });

  describe('GET /api/v1/users/:id', () => {
    it('should return user data for valid ID', async () => {
      const testUser = await User.findOne({
        where: { email: '<EMAIL>' },
      });

      await UserProfile.create({
        user_id: testUser.id,
        phone_number: '1234567890',
        location: 'New York',
        manager: 'John Doe',
        years_experience: 5,
        performance_rating: 4.5,
        last_promotion: '2024-01-01',
        education: 'Bachelor of Engineering',
        competencies: ['Leadership', 'Communication'],
        skills: ['JavaScript', 'React'],
        current_position: {
          role_name: 'Software Engineer',
          department: 'Engineering',
          job_grade: 'L5',
        },
      });

      const response = await api.as(admin).get(`/api/v1/users/${testUser.id}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toMatchObject({
        id: testUser.id,
        name: testUser.name,
        email: testUser.email,
        current_position: {
          role_name: 'Software Engineer',
          department: 'Engineering',
          job_grade: 'L5',
        },
        phone_number: '1234567890',
        location: 'New York',
        manager: 'John Doe',
        years_experience: 5,
        performance_rating: 4.5,
        last_promotion: '2024-01-01',
        education: 'Bachelor of Engineering',
        competencies: ['Leadership', 'Communication'],
        skills: ['JavaScript', 'React'],
      });
    });

    it('should return 404 for non-existent user', async () => {
      const response = await api.as(admin).get('/api/v1/users/999');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('User not found');
    });

    it('should return 401 without authentication', async () => {
      const response = await api.get('/api/v1/users/1');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 403 for non-admin user', async () => {
      const response = await api.as(user).get('/api/v1/users/1');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });
});
