const api = require('../utils/requestHelper');
const { describeWithTransaction } = require('../utils/describeWithTransaction');
const { User, JobTitle, JobVacancy } = require('../../src/models');

describeWithTransaction('JobVacanciesController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin',
    });

    user = await User.create({
      name: 'Regular User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user',
    });
  });

  describe('GET /api/v1/job_vacancies', () => {
    beforeEach(async () => {
      const jobTitle = await JobTitle.create({
        name: 'Software Engineer',
        prefilled_details: {},
      });

      await JobVacancy.create({
        name: 'Software Engineer',
        department: 'Engineering',
        job_grade: 'L5',
        job_title_id: jobTitle.id,
      });
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/job_vacancies');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/job_vacancies');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });

      it('should return 200 for admin users', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(Array.isArray(response.body.data)).toBe(true);
      });
    });

    describe('successful responses', () => {
      it('should return all job vacancies with correct format', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data).toHaveLength(1);

        // Check the structure of job vacancy objects
        const jobVacancy = response.body.data[0];
        expect(jobVacancy).toHaveProperty('id');
        expect(jobVacancy).toHaveProperty('name');
      });

      it('should include pagination information', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.body.pagination).toHaveProperty('page');
        expect(response.body.pagination).toHaveProperty('limit');
        expect(response.body.pagination).toHaveProperty('total');
        expect(response.body.pagination.total).toBe(1);
      });
    });

    describe('pagination and filtering', () => {
      beforeEach(async () => {
        // Create another job vacancies
        const jobTitle = await JobTitle.findOne({ where: { name: 'Software Engineer' } });
        await JobVacancy.bulkCreate([
          {
            name: 'Product Engineer',
            department: 'Engineering',
            job_grade: 'L5',
            job_title_id: jobTitle.id,
          },
          {
            name: 'Vibe Coder',
            department: 'Engineering',
            job_grade: 'L5',
            job_title_id: jobTitle.id,
          },
        ]);
      });

      it('should support pagination', async () => {
        const params = { page: 1, limit: 2 };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.pagination.page).toBe(1);
        expect(response.body.pagination.limit).toBe(2);
        expect(response.body.pagination.total).toBe(3);
      });

      it('should support search filtering', async () => {
        const params = { search: 'Vibe' };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].name).toBe('Vibe Coder');
      });

      it('should support sorting', async () => {
        const params = { sort: 'name', sort_direction: 'desc' };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.data[0].name).toBe('Vibe Coder');
        expect(response.body.data[1].name).toBe('Software Engineer');
        expect(response.body.data[2].name).toBe('Product Engineer');
      });
    });

    describe('empty results', () => {
      it('should return empty array when no job vacancies exist', async () => {
        // Clear all job vacancies
        await JobVacancy.destroy({ where: {}, truncate: true, cascade: true });

        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.total).toBe(0);
      });

      it('should return empty array when filter matches nothing', async () => {
        const params = { search: 'NonexistentTitle' };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
      });
    });
  });

  describe('GET /api/v1/job_vacancies/:id', () => {
    let jobVacancyId;

    beforeEach(async () => {
      const jobTitle = await JobTitle.create({
        name: 'Software Engineer',
        prefilled_details: {},
      });

      await JobVacancy.create({
        name: 'Software Engineer',
        department: 'Engineering',
        job_grade: 'L5',
        job_title_id: jobTitle.id,
      });

      jobVacancyId = (await JobVacancy.findOne()).id;
    });

    it('should return job vacancy for valid ID', async () => {
      const response = await api.as(admin).get(`/api/v1/job_vacancies/${jobVacancyId}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', jobVacancyId);
    });

    it('should return 404 for non-existent job vacancy', async () => {
      const response = await api.as(admin).get('/api/v1/job_vacancies/0');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Job vacancy not found');
    });

    it('should return 401 without authentication', async () => {
      const response = await api.get(`/api/v1/job_vacancies/${jobVacancyId}`);

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 403 for non-admin users', async () => {
      const response = await api.as(user).get(`/api/v1/job_vacancies/${jobVacancyId}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('POST /api/v1/job_vacancies', () => {
    beforeEach(async () => {
      const jobTitle = await JobTitle.create({
        name: 'Software Engineer',
        prefilled_details: {},
      });

      await JobVacancy.create({
        name: 'Software Engineer',
        department: 'Engineering',
        job_grade: 'L5',
        job_title_id: jobTitle.id,
      });
    });

    it('should create new job vacancy', async () => {
      const jobTitle = await JobTitle.findOne({ where: { name: 'Software Engineer' } });
      const jobVacancyData = {
        job_title_id: jobTitle.id,
        department: 'Engineering',
        job_grade: 'L5',
      };

      const response = await api.as(admin).post('/api/v1/job_vacancies', jobVacancyData);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('name', 'Software Engineer');
    });

    it('should return 401 without authentication', async () => {
      const response = await api.post('/api/v1/job_vacancies');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 403 for non-admin users', async () => {
      const response = await api.as(user).post('/api/v1/job_vacancies');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });
});
