const request = require('supertest');
const app = require('../../src/app');
const authService = new (require('../../src/services/AuthService'))();

// Example usage:
// const api = require('./requestHelper');

// Unauthenticated request
// const res = await api.get('/public-route');

// Authenticated request
// const userProfile = await api.as(user).get('/profile');

// Authenticated request with body
// const createdPost = await api.as(user).post('/posts', { title: 'New Post' });

class ApiClient {
  constructor(app) {
    this.app = app;
    this.user = null;
  }

  // Set the user for the next request
  as(user) {
    this.user = user;
    return this; // Enable chaining
  }

  // Private method to build the core request
  #createRequest(method, url) {
    const testRequest = request(this.app)[method](url);
    if (this.user) {
      const token = authService.generateAuthToken(this.user);
      testRequest.set('Authorization', `Bearer ${token}`);
    }

    this.user = null;
    return testRequest;
  }

  get(url, query = {}) {
    return this.#createRequest('get', url).query(query);
  }

  post(url, body = {}) {
    return this.#createRequest('post', url).send(body);
  }

  put(url, body = {}) {
    return this.#createRequest('put', url).send(body);
  }

  delete(url) {
    return this.#createRequest('delete', url);
  }

  options(url, headers = {}) {
    return this.#createRequest('options', url).set(headers);
  }
}

module.exports = new ApiClient(app);
