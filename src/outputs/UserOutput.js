const ApiOutput = require('./ApiOutput');

/**
 * Output formatting class for user responses
 */
class UserOutput extends ApiOutput {
  /**
   * Format the user output data
   * @returns {Object} Formatted user data
   */
  format() {
    return {
      id: this.data.id,
      name: this.data.name,
      email: this.data.email,
      role: this.data.role,
    };
  }

  showFormat() {
    const user = this.data;
    const profile = user.profile || {};
    const currentPosition = profile.current_position || {};

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      current_position: {
        role_name: currentPosition.role_name,
        department: currentPosition.department,
        job_grade: currentPosition.job_grade,
      },
      phone_number: profile.phone_number,
      location: profile.location,
      manager: profile.manager,
      years_experience: profile.years_experience,
      performance_rating: profile.performance_rating,
      last_promotion: profile.last_promotion,
      education: profile.education,
      competencies: profile.competencies || [],
      skills: profile.skills || [],
    };
  }
}

module.exports = UserOutput;
