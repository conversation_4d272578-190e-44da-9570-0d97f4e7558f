const { Op } = require('sequelize');
const BaseRepository = require('./BaseRepository');
const { JobVacancy } = require('../models');

class JobVacanciesRepository extends BaseRepository {
  constructor() {
    super(JobVacancy);
  }

  /**
   * Filter job vacancies by ID
   * Usage: /api/v1/job_vacancies?id=1
   * @param {number} id - Job vacancy ID
   * @returns {Object} Where condition for ID filter
   */
  filterById(id) {
    return { id };
  }

  /**
   * Filter job vacancies by search term in name
   * Usage: /api/v1/job_vacancies?search=developer
   * @param {string} search - Search term
   * @returns {Object} Where condition for search filter
   */
  filterBySearch(search) {
    if (!search) return null;

    return {
      name: {
        [Op.iLike]: `%${search}%`,
      },
    };
  }
}

module.exports = JobVacanciesRepository;
