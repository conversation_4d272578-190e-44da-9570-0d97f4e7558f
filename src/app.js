const express = require('express');
const cors = require('cors');
const compression = require('compression');
const helmet = require('helmet');

const config = require('./config/config');

// Import middleware
const { rateLimiters } = require('./middlewares/rateLimiter');
const errorHandler = require('./middlewares/errorHandler');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const userPositionsRoutes = require('./routes/userPositions');
const jobTitleRoutes = require('./routes/jobTitles');
const jobVacancyRoutes = require('./routes/jobVacancies');

const app = express();

// Express middlewares
app.use(compression());
app.use(helmet());
app.disable('x-powered-by');

// Rate limiting middleware (apply to all routes)
app.use(rateLimiters.general);

// CORS configuration
app.use(
  cors({
    origin: config.corsOrigin || '*',
    credentials: true,
  }),
);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/api/v1/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: config.nodeEnv || 'development',
  });
});

// API routes with specific rate limiting
app.use('/api/v1/auth', rateLimiters.auth, authRoutes);
app.use('/api/v1/users', userRoutes);
app.use('/api/v1/user_positions', rateLimiters.general, userPositionsRoutes);
app.use('/api/v1/job_titles', rateLimiters.general, jobTitleRoutes);
app.use('/api/v1/job_vacancies', rateLimiters.general, jobVacancyRoutes);

// Error handler middleware
app.use(errorHandler);

module.exports = app;
