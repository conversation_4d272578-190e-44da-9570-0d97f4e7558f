const AppService = require('./AppService');
const { JobVacancy, JobTitle } = require('../models');
const JobVacanciesRepository = require('../repositories/JobVacanciesRepository');

class JobVacancyService extends AppService {
  constructor() {
    super();
    this.repository = new JobVacanciesRepository();
  }

  /**
   * Get all job vacancies
   * @param {Object} params - Query params (page, limit, filters, etc.)
   * @returns {Object} Job vacancies array and pagination info
   */
  async findAll(params = {}) {
    const { rows, pagination } = await this.repository.findAll(params);
    return { job_vacancies: rows, pagination };
  }

  /**
   * Find a job vacancy by ID
   * @param {number} id - Job vacancy ID
   * @returns {Object} Job vacancy object
   * @throws {NotFoundError} If job vacancy is not found
   */
  async findById(id) {
    const vacancy = await this.repository.findOne({ id });
    this.exists(vacancy, 'Job vacancy not found');
    return vacancy;
  }

  /**
   * Create a new job vacancy
   * @param {Object} data - Job vacancy data
   * @returns {Object} Created job vacancy
   * @throws {NotFoundError} If job title is not found
   */
  async create(data) {
    const jobTitle = await JobTitle.findByPk(data.job_title_id);
    this.exists(jobTitle, 'Job title not found');

    const vacancyData = {
      ...data,
      name: jobTitle.name,
    };

    const vacancy = await JobVacancy.create(vacancyData);
    return vacancy;
  }
}

module.exports = JobVacancyService;
