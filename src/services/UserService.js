const AppService = require('./AppService');
const UsersRepository = require('../repositories/UsersRepository');
const { User, UserProfile } = require('../models');

class UserService extends AppService {
  constructor() {
    super();
    this.repository = new UsersRepository();
  }

  /**
   * Get all users with pagination
   * @param {Object} params - Query params (page, limit, etc.)
   * @returns {Object} - Users array and pagination info
   */
  async findAll(params = {}) {
    const { rows, pagination } = await this.repository.findAll(params);
    return { users: rows, pagination };
  }

  /**
   * Find user by ID with profile data
   * @param {number} id - User ID
   * @returns {Object} - User with profile data
   */
  async findById(id) {
    const user = await User.findByPk(id, {
      include: [{ model: UserProfile, as: 'profile' }],
    });
    this.exists(user, 'User not found');
    return user;
  }
}

module.exports = UserService;
