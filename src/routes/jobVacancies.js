const express = require('express');
const jobVacanciesController = require('../controllers/JobVacanciesController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

/**
 * Apply admin-only authorization to all routes in this file
 * All job vacancy endpoints require admin role
 */
router.use(authenticateToken, requireRole('admin'));

/**
 * @route GET /api/v1/job_vacancies
 * @desc Get all job vacancies
 * @access Private (Admin)
 */
router.get('/', jobVacanciesController.index);

/**
 * @route POST /api/v1/job_vacancies
 * @desc Create a new job vacancy
 * @access Private (Admin)
 */
router.post('/', jobVacanciesController.create);

/**
 * @route GET /api/v1/job_vacancies/:id
 * @desc Get a specific job vacancy by ID
 * @access Private (Admin)
 */
router.get('/:id', jobVacanciesController.show);

module.exports = router;
