const ApplicationInput = require('./ApplicationInput');

class UsersIndexInput extends ApplicationInput {
  /**
   * Define the JSON schema for listing users validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        name: {
          type: 'string',
        },
        email: {
          type: 'string',
          format: 'email',
        },
        role: {
          type: 'string',
          enum: ['admin', 'user'],
        },
        created_after: {
          type: 'string',
          format: 'date',
        },
        created_before: {
          type: 'string',
          format: 'date',
        },
        sort: {
          type: 'string',
          enum: ['id', 'name', 'email', 'role', 'created_at', 'updated_at'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
      },
      required: [],
    };
  }
}

module.exports = UsersIndexInput;
