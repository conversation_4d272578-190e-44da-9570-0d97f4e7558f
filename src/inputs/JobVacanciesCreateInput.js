const ApplicationInput = require('./ApplicationInput');

class JobVacanciesCreateInput extends ApplicationInput {
  /**
   * Get the JSON schema for validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        job_title_id: {
          type: 'integer',
          minimum: 1,
        },
        department: {
          type: 'string',
        },
        job_grade: {
          type: 'string',
        },
        job_description: {
          type: 'string',
        },
        competencies: {
          type: 'array',
          items: {
            type: 'string',
          },
          default: [],
        },
        skills: {
          type: 'array',
          items: {
            type: 'string',
          },
          default: [],
        },
      },
      required: ['job_title_id', 'department', 'job_grade'],
      additionalProperties: false,
    };
  }
}

module.exports = JobVacanciesCreateInput;
