'use strict';
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class JobVacancy extends AppModel {
  /**
   * Associations with other models
   */
  static associate(models) {
    this.belongsTo(models.JobTitle, {
      foreignKey: 'job_title_id',
      as: 'jobTitle',
    });
  }

  /**
   * Model schema
   */
  static schema() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: {
            msg: 'Job vacancy name cannot be empty',
          },
          len: {
            args: [2, 255],
            msg: 'Job vacancy name must be between 2 and 255 characters',
          },
        },
      },
      department: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: {
            msg: 'Department cannot be empty',
          },
          len: {
            args: [2, 255],
            msg: 'Department must be between 2 and 255 characters',
          },
        },
      },
      job_grade: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: {
            msg: 'Job grade cannot be empty',
          },
          len: {
            args: [1, 255],
            msg: 'Job grade must be between 1 and 255 characters',
          },
        },
      },
      job_description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      competencies: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        defaultValue: [],
        validate: {
          isValidArray(value) {
            if (value !== null && !Array.isArray(value)) {
              throw new Error('Competencies must be an array');
            }
          },
        },
      },
      skills: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        defaultValue: [],
        validate: {
          isValidArray(value) {
            if (value !== null && !Array.isArray(value)) {
              throw new Error('Skills must be an array');
            }
          },
        },
      },
      job_title_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
          notNull: {
            msg: 'Job title ID is required',
          },
          isInt: {
            msg: 'Job title ID must be an integer',
          },
          min: {
            args: [1],
            msg: 'Job title ID must be a positive integer',
          },
        },
      },
    };
  }

  /**
   * Model options
   */
  static options() {
    return {
      tableName: 'job_vacancies',
    };
  }

  /**
   * Lifecycle hooks
   */
  static hooks() {
    return {
      beforeUpdate: jobVacancy => {
        jobVacancy.updated_at = new Date();
      },
    };
  }
}

module.exports = JobVacancy;
