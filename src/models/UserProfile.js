const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class UserProfile extends AppModel {
  static schema() {
    return {
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      phone_number: { type: DataTypes.STRING },
      location: { type: DataTypes.STRING },
      manager: { type: DataTypes.STRING },
      current_position: { type: DataTypes.JSONB },
      years_experience: { type: DataTypes.INTEGER },
      performance_rating: { type: DataTypes.FLOAT },
      last_promotion: { type: DataTypes.DATEONLY },
      education: { type: DataTypes.STRING },
      competencies: { type: DataTypes.ARRAY(DataTypes.STRING) },
      skills: { type: DataTypes.ARRAY(DataTypes.STRING) },
    };
  }

  static associate(models) {
    this.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });
  }
}

module.exports = UserProfile;
